import { WrsDepartmentReportRepository } from '../infra/WrsDepartmentReport.repository'
import { WrsDepartmentReport } from '../domain/WrsDepartmentReport'
import { DepartmentReportStatus, ReportStatus, WrsDepartmentReportProps } from '../domain/domain.schema'
import { DepartmentReportPaginationQuery, DepartmentReportPaginationQuerySchema, ListDepartmentReportQuery, ListDepartmentReportQuerySchema } from '../domain/query.schema'
import { StatisticsDepartmentReportResponse } from '../domain/response.schema';
import { LlmService } from '@/infra/llm';
import { LlmSummaryReportResponse } from './service.schema';
import { WrsWeekRepository } from '../infra/WrsWeek.repository';
import { JwtPayload } from '@/module/auth/auth.schema';
import { WrsWorkItemRepository } from '../infra/WrsWorkItem.repository';
import { getPrisma } from "@/infra/db"

export class WrsDepartmentReportService {

    constructor(
        private readonly repo: WrsDepartmentReportRepository,
        private readonly wrsWeekRepo: WrsWeekRepository,
        private readonly wrsWorkItemRepo: WrsWorkItemRepository,
    ) { }

    /**
     * 列出「所有部門」與統計
     */
    async statistics(userId: string, params?: ListDepartmentReportQuery): Promise<StatisticsDepartmentReportResponse[]> {
        const safeParams: ListDepartmentReportQuery = ListDepartmentReportQuerySchema.partial().parse(params);
        const result = await this.repo.statistics(userId, safeParams);
        return result
    }
    /**
     * 查詢所有部門週報列表，支援分頁與條件
     */
    async paging(params?: DepartmentReportPaginationQuery): Promise<{ data: WrsDepartmentReport[], total: number }> {
        const safeParams: DepartmentReportPaginationQuery = DepartmentReportPaginationQuerySchema.parse(params);

        // Manually validate and transform the status parameter
        let status: DepartmentReportStatus | undefined = undefined;
        if (safeParams.status) {
            if (safeParams.status === 'draft' || safeParams.status === 'submitted') {
                status = safeParams.status as DepartmentReportStatus;
            }
        }

        const repoParams = {
            ...safeParams,
            status: status,
        };

        return this.repo.pagingList(repoParams);
    }

    /**
     * 建立部門週報草稿
     */
    async create(user: JwtPayload, reportProps: WrsDepartmentReportProps): Promise<WrsDepartmentReport> {
        const currentWeek = await this.wrsWeekRepo.getOrCreateCurrentWeek(new Date());
        const orgId = user.assignments.find(assignment => assignment.assignmentType === 'primary')?.orgId;
        return this.repo.create({
            ...reportProps,
            reviewedBySupervisor: false,
            id: `org${currentWeek.getProps().id}_${orgId}`,
            status: ReportStatus.draft,
            summaryEmployeeId: user.id,
            weekId: currentWeek.getProps().id,
            orgId: orgId
        });
    }

    /**
     * 查詢單一部門週報
     */
    async getById(id: string): Promise<WrsDepartmentReport> {
        return this.repo.findById(id);
    }

    /**
     * 更新部門週報草稿
     */
    async updateDraft(id: string, updateProps: Partial<WrsDepartmentReportProps>): Promise<WrsDepartmentReport> {
        return this.repo.updateDraft(id, updateProps);
    }

    /**
     * 提交部門週報
     */
    async submit(user: JwtPayload, reportProps: WrsDepartmentReportProps): Promise<WrsDepartmentReport> {
        const orgId = user.assignments.find(assignment => assignment.assignmentType === 'primary')?.orgId;
        const currentWeek = await this.wrsWeekRepo.getOrCreateCurrentWeek(new Date());
        if (!orgId) {
            throw new Error(`Organization not found`);
        }

        const prisma = getPrisma();
        return await prisma.$transaction(async (tx) => {
            const result = await this.repo.create({
                ...reportProps,
                reviewedBySupervisor: false,
                id: `org${currentWeek.getProps().id}_${orgId}`,
                status: ReportStatus.submitted,
                summaryEmployeeId: user.id,
                weekId: currentWeek.getProps().id,
                orgId: orgId
            }, tx);
            await this.wrsWorkItemRepo.updateManyStatus({ weekId: currentWeek.getProps().id, orgId: orgId, status: 'submitted' }, 'done', tx);
            return result;
        });
    }
    /**
     * 標記部門週報為已審閱
     */
    async markReviewed(id: string): Promise<WrsDepartmentReport> {
        const report = await this.repo.findById(id);
        if (report.getProps().status !== 'submitted') {
            throw new Error('Only submitted reports can be marked as reviewed');
        }
        return this.repo.markReviewed(id);
    }

    /**
     * ai 彙整
     */
    async aiSummary(user: JwtPayload, weekId: string): Promise<LlmSummaryReportResponse> {
        const orgId = user.assignments.find(assignment => assignment.assignmentType === 'primary')?.orgId;
        if (!orgId) {
            throw new Error(`Organization not found`);
        }
        const orgName = await this.repo.getOrgNameByOrgId(orgId)
        const list = await this.repo.getDepAllMemberReportByOrgId(orgId, weekId);
        try {
            const llm = LlmService.getInstance()
            const result = await llm.summaryReport(orgName, JSON.stringify(list));
            return result;
        }
        catch (error) {
            console.error('AI Summary Error:', error);
            throw new Error('AI Summary failed, please try again later.');
        }
    }

    /**
     * Dashboard 資料查詢 - 取得公司概況和部門摘要分佈
     */
    async dashboard(userId: string, weekId: string, previousWeekId?: string) {
        return this.repo.dashboard(userId, weekId, previousWeekId);
    }
}
