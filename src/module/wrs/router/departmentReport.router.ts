import { Hono, Context } from 'hono'
import { validator } from "hono-openapi/zod";
import { WrsDepartmentReportPropsSchema } from '../domain/domain.schema'
import { WrsDepartmentReportService } from '../app/WrsDepartmentReport.service'
import { jwtAuthMiddleware } from '@/middleware/auth'
import { openApiTag } from '@/middleware/openapi'
import { ItemResponse, ListResponse, PagingResponse } from '@/module/common/schema'
import { DepartmentReportPaginationQuerySchema, ListDepartmentReportQuerySchema } from '../domain/query.schema';
import { StatisticsDepartmentReportResponseSchema, DashboardResponseSchema } from '../domain/response.schema';
import { LlmSummaryReportResponseSchema } from '../app/service.schema';
import { checkPermission } from '@/middleware/checkPermission';
import { JwtPayload } from '@/module/auth/auth.schema';

export function createWrsDepartmentReportRouter(
    departmentReportService: WrsDepartmentReportService,
) {
    const router = new Hono()
    router.use('*', jwtAuthMiddleware)
    const openApiMiddleware = openApiTag("DepartmentReport");

    /**
     * TODO:
     * GET /v0/wrs/department-reports/statistics     列出「所有部門」與統計(可透過query指定週報狀態)=> done
     * GET  /v0/wrs/department-reports         查詢部門週報列表
     * POST /v0/wrs/department-reports         儲存部門週報（草稿）
     * GET  /v0/wrs/department-reports/:id   查詢單一部門週報
     * PATCH /v0/wrs/department-reports/:id   更新部門週報（草稿）
     * GET /v0/wrs/department-reports/:id/dashboard   查詢部門週報成員列表
     * GET /v0/wrs/department-reports/:id/ai-summary    AI生成部門週報摘要
     * POST /v0/wrs/department-reports/:id/submit    提交部門週報
     * POST /v0/wrs/department-reports/:id/mark-reviewed   處主管標記已審閱
     */

    router.get('/dashboard',
        openApiMiddleware({
            description: "Dashboard 資料查詢 - 取得公司概況和部門摘要分佈",
            responsesSchema: ItemResponse(DashboardResponseSchema)
        }),
        // checkPermission(`department-report:read:all`),
        validator("query", ListDepartmentReportQuerySchema.pick({ weekId: true }).partial()),
        async (c: Context) => {
            const query = c.req.query();
            const user = c.get('user');
            const dashboardData = await departmentReportService.dashboard(user.id, query.weekId);
            return c.json({
                success: true,
                data: dashboardData
            });
        }
    );

    router.get('/statistics',
        openApiMiddleware({
            description: "列出「所有部門」與統計(可透過query指定週報狀態)",
            responsesSchema: ListResponse(StatisticsDepartmentReportResponseSchema)
        }),
        // checkPermission(`department-report:read:all`),
        validator("query", ListDepartmentReportQuerySchema), // TODO: add query schema
        async (c: Context) => {
            const query = c.req.query();
            const user = c.get('user');
            const reports = await departmentReportService.statistics(user.id, query); // TODO: pass user id and query params
            return c.json({
                success: true,
                total: reports.length,
                data: reports
            });
        }
    );

    router.get('/',
        openApiMiddleware({
            description: "查詢部門週報列表",
            responsesSchema: PagingResponse(WrsDepartmentReportPropsSchema)
        }),
        checkPermission(`department-report:read:all`),
        validator("query", DepartmentReportPaginationQuerySchema), // TODO: add query schema
        async (c: Context) => {
            const query = c.req.query();
            const user = c.get('user');
            const reports = await departmentReportService.paging(query); // TODO: pass user id and query params
            return c.json({
                success: true,
                total: reports.total,
                data: reports.data.map(item => item.getProps())
            });
        }
    );

    /** 前端沒有編輯功能, 先整合進submit直接提出表單內容
    router.post('/',
        openApiMiddleware({
            description: "部門視角, 儲存部門週報（草稿）",
            responsesSchema: ItemResponse(WrsDepartmentReportPropsSchema)
        }),
        checkPermission(`department-report:create:department`),
        validator("json", WrsDepartmentReportPropsSchema.pick({
            summary: true,
            highlight: true,
            plan: true
        })),
        async (c: Context) => {
            const body = await c.req.json();
            const user = c.get('user') as JwtPayload;
            const report = await departmentReportService.create(user, body);
            return c.json({ success: true, data: report.getProps() });
        }
    );
    */

    router.get('/:id',
        openApiMiddleware({
            description: "查詢單一部門週報",
            responsesSchema: ItemResponse(WrsDepartmentReportPropsSchema)
        }),
        checkPermission(`department-report:read:all`),
        async (c: Context) => {
            const id = c.req.param('id');
            const report = await departmentReportService.getById(id);
            return c.json({ success: true, data: report.getProps() });
        }
    );

    /**
    router.patch('/:id',
        openApiMiddleware({
            description: "更新部門週報草稿",
            responsesSchema: ItemResponse(WrsDepartmentReportPropsSchema)
        }),
        validator("json", WrsDepartmentReportPropsSchema.pick({
            summary: true,
            highlight: true,
            plan: true
        }).partial()),
        async (c: Context) => {
            const id = c.req.param('id');
            const body = await c.req.json();
            const report = await departmentReportService.updateDraft(id, body);
            return c.json({ success: true, data: report.getProps() });
        }
    );
     */

    router.get('/:weekId/ai-summary',
        openApiMiddleware({
            description: "部門視角, AI生成部門週報摘要",
            responsesSchema: ItemResponse(LlmSummaryReportResponseSchema)
        }),
        checkPermission(`department-report:update:department`),
        async (c: Context) => {
            const weekId = c.req.param('weekId');
            const user = c.get('user') as JwtPayload;
            const report = await departmentReportService.aiSummary(user, weekId);
            return c.json({ success: true, data: report });
        }
    );
    
    router.post('/submit',
        openApiMiddleware({
            description: "部門視角, 提交部門週報",
            responsesSchema: ItemResponse(WrsDepartmentReportPropsSchema)
        }),
        checkPermission(`department-report:submit:department`),
        validator("json", WrsDepartmentReportPropsSchema.pick({
            summary: true,
            highlight: true,
            plan: true
        })),
        async (c: Context) => {
            const body = await c.req.json();
            const user = c.get('user') as JwtPayload;
            const report = await departmentReportService.submit(user, body);
            return c.json({ success: true, data: report.getProps() });
        }
    );

    /** 
    router.post('/:id/mark-reviewed',
        openApiMiddleware({
            description: "處主管標記部門週報已審閱",
            responsesSchema: ItemResponse(WrsDepartmentReportPropsSchema)
        }),
        async (c: Context) => {
            const id = c.req.param('id');
            const report = await departmentReportService.markReviewed(id);
            return c.json({ success: true, data: report.getProps() });
        }
    );
    */

    return router
}